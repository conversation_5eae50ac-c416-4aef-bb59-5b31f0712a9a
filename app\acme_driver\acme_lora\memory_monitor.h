/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * 内存监控和泄漏检测工具
 * 用于监控FCM设备数据处理过程中的内存使用情况
 **/

#ifndef ACME_LORA_MEMORY_MONITOR_H
#define ACME_LORA_MEMORY_MONITOR_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// 内存监控配置
#define MEM_MONITOR_ENABLED     1       // 是否启用内存监控
#define MEM_MONITOR_INTERVAL    30      // 监控间隔(秒)
#define MEM_MONITOR_LOG_THRESHOLD 1024  // 内存增长阈值(KB)

// 内存统计结构
typedef struct {
    uint64_t total_alloc;       // 总分配内存
    uint64_t total_free;        // 总释放内存
    uint64_t current_usage;     // 当前使用内存
    uint64_t peak_usage;        // 峰值使用内存
    uint32_t alloc_count;       // 分配次数
    uint32_t free_count;        // 释放次数
    time_t   last_check;        // 上次检查时间
    uint64_t last_rss;          // 上次RSS内存
} memory_stats_t;

// 内存监控器结构
typedef struct {
    memory_stats_t stats;
    FILE *log_file;
    int enabled;
} memory_monitor_t;

// 全局内存监控器
extern memory_monitor_t g_mem_monitor;

// 内存监控函数
int memory_monitor_init(const char *log_file_path);
void memory_monitor_cleanup(void);
void memory_monitor_check(const char *context);
uint64_t get_process_memory_usage(void);
void memory_monitor_log_stats(const char *context);

// 内存分配跟踪宏（仅在调试模式下启用）
#ifdef DEBUG_MEMORY
#define MONITORED_MALLOC(size) monitored_malloc(size, __FILE__, __LINE__)
#define MONITORED_FREE(ptr) monitored_free(ptr, __FILE__, __LINE__)
#define MONITORED_CALLOC(count, size) monitored_calloc(count, size, __FILE__, __LINE__)

void* monitored_malloc(size_t size, const char *file, int line);
void monitored_free(void *ptr, const char *file, int line);
void* monitored_calloc(size_t count, size_t size, const char *file, int line);
#else
#define MONITORED_MALLOC(size) malloc(size)
#define MONITORED_FREE(ptr) free(ptr)
#define MONITORED_CALLOC(count, size) calloc(count, size)
#endif

// 便捷宏：在关键函数入口和出口处检查内存
#define MEM_CHECK_ENTER(func_name) \
    do { \
        if (MEM_MONITOR_ENABLED) { \
            memory_monitor_check("ENTER_" func_name); \
        } \
    } while(0)

#define MEM_CHECK_EXIT(func_name) \
    do { \
        if (MEM_MONITOR_ENABLED) { \
            memory_monitor_check("EXIT_" func_name); \
        } \
    } while(0)

#ifdef __cplusplus
}
#endif

#endif // ACME_LORA_MEMORY_MONITOR_H
