#include "otel/otel_manager.h"
#include "utils/asprintf.h"
#include "utils/time.h"
#include <stdlib.h>
#include <neuron.h>
#include <assert.h>
#include <fcntl.h>
#include <signal.h>
#include <sys/resource.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#include <errno.h>
#include <stdio.h>
#include <string.h>
#include "util.h"
#include "errcodes.h"
#include "CRC16.h"
#include "acme_lora_driver.h"
#include "lora_protocol.h"

/*
* Lora 串口连接通信连接事件
*/
void lora_tty_conn_connected(void *data, int fd)
{
    struct neu_plugin *plugin = (struct neu_plugin *) data;
    (void) fd;

    plugin->tty_sta = NEU_NODE_LINK_STATE_CONNECTED;
}

/*
 *  Lora 串口连接通信断开连接事件
 */
void lora_tty_conn_disconnected(void *data, int fd)
{
    struct neu_plugin *plugin = (struct neu_plugin *) data;
    (void) fd;

    plugin->tty_sta = NEU_NODE_LINK_STATE_DISCONNECTED;
}

/*
* Lora 模块 RST 脚初始化设置为输出模式
*/
int lora_reset_gpio_init()
{
    int ret = 0;
     int gpio_fd = -1;
    char path[64]={0};

    //GPIO口  ---- 后续 GPIO 申请命令 放在 启动脚本 "echo 53 > /sys/class/gpio/export"
    int fd = open("/sys/class/gpio/export", O_WRONLY);
    if (fd < 0) {
        perror("Export open failed");
        exit(1);
    }
    dprintf(fd, "%d", SPT_LORA_RST_PIN_NUM);
    close(fd);
    printf("1、Lora rst pin: GPIO%d request success \r\n", SPT_LORA_RST_PIN_NUM);

    usleep(100000);

    // 配置为输出模式
    snprintf(path, sizeof(path), "/sys/class/gpio/gpio%d/direction", SPT_LORA_RST_PIN_NUM);
    gpio_fd = open(path, O_WRONLY);
    if (gpio_fd < 0) {
        perror("Open GPIO direction failed");
        return -1;
    }
    
    if (write(gpio_fd, "out", 3) != 3) {
        perror("Set GPIO direction failed");
        close(gpio_fd);
        return -1;
    }

    printf("2、Lora rst pin: GPIO%d init success \r\n", SPT_LORA_RST_PIN_NUM);
    close(gpio_fd);
    return ret;
}

/*
* Lora reset pin脚 电平设置
*/
int lora_reset_gpio_set(int value)
{
    int ret = 0;
    int gpio_fd = -1;

    char val = value ? '1' : '0';
    char path[64];
    
    if (gpio_fd < 0) {
        snprintf(path, sizeof(path), "/sys/class/gpio/gpio%d/value", SPT_LORA_RST_PIN_NUM);
        gpio_fd = open(path, O_WRONLY);
    }
    
    if (gpio_fd >= 0) {
        write(gpio_fd, &val, 1);
        printf("Lora rst pin: GPIO%d set %c \r\n", SPT_LORA_RST_PIN_NUM,val);
    }

    return ret;
}

void reset_lora_Ra07_mode()
{
    lora_reset_gpio_set(0);
    usleep(100000);
    lora_reset_gpio_set(1);
    printf("wavemesh reset now\n");
}



void atwr_wave_mesh_module_cfg(neu_plugin_t *plugin)
{
    char uart_lora_write_buf[200] = {0x55,0xcc,0x05,0x01,0x27,0x00,0x00,0x00,0x00,0x00};
    uint8_t cfg[8] = {'A','T','W','R',0x03,0x00,0xff,0x0d};
    int length=4;

    memcpy(uart_lora_write_buf, cfg, length);
    int len = sizeof(cfgBlock) - 55 + 32;
    uart_lora_write_buf[length++] = len+2;
    uart_lora_write_buf[length++] = 0x00; //start addr
    memcpy(&uart_lora_write_buf[length], (char *)plugin->wm_cfg, len);
    length += len;
    uart_lora_write_buf[length++] = 0x0d; //end flag


    printf("atwr_cfg-(%d)-->",len+2);
    for(int i=0;i<length;i++){
        printf("%02x ",uart_lora_write_buf[i]);
    }
    printf("<--lora_snd\n");

    reset_lora_Ra07_mode();
    sleep(1);

    //write(lora_fd, uart_lora_write_buf, length);
    //写如串口
    neu_conn_send(plugin->lora_tty_conn, uart_lora_write_buf, length);

    //write(lora_fd, uart_lora_write_buf, length);
    
    //sleep(1);
    //write(lora_fd, uart_lora_write_buf, length);
    usleep(500000);
    reset_lora_Ra07_mode();
}


/*
* 网关进入配对模式，调整频段、信道、网络ID，捕获子设备的入网请求
*/
void  pair_on_change_chn(neu_plugin_t *plugin,uint8_t freq, uint8_t chn, uint16_t netid)
{
    nlog_debug("Lora pair parameter change freq=%d || chn=%d || netid=%d ",freq,chn,netid);
    plugin->wm_cfg->flEmpty.bits.freq_band = freq;                                     //频段 0-433Mhz 1-462Mhz 2-478Mhz 3-502Mhz
    plugin->wm_cfg->flag0.bits.pa_index = 0;                                           //功率 -20dbm
    plugin->wm_cfg->chnBaud.bits.signalChn = chn;                                      //信道 1
    plugin->wm_cfg->chnBaud.bits.baudIndex = CFG_CHN_BAUD;                             //波特率必须115200
    plugin->wm_cfg->flWake = 0;                                                        //距离等级0
    plugin->wm_cfg->netID[0] = 0;
    plugin->wm_cfg->netID[1] = 0;
    plugin->wm_cfg->netID[2] = netid;
    atwr_wave_mesh_module_cfg(plugin);//临时更新网关本身无线配置
}


int lora_pair_timer_callback(void *arg)
{
    neu_plugin_t *plugin = (neu_plugin_t *)arg;
    if(plugin == NULL){
        nlog_debug("pair timer parameter error.");
        return -1;
    }

    nlog_debug("**** pair timer handler . mode=%d ****", plugin->pair_mode);

    switch (plugin->pair_mode)
    {
    case 0:{
        pair_on_change_chn(plugin,1,3,3);
        plugin->pair_mode = 1;
        break;
    }
    case 1:{
        pair_on_change_chn(plugin,3,4,31);
        plugin->pair_mode = 2;
        break;
    }
    case 2:{
        pair_on_change_chn(plugin,0,0,0);
        plugin->pair_mode = 0;
        break;
    }
    case 3:{
        acme_lora_driver_stop_search(plugin);       //停止搜网
        plugin->pair_mode = -1;
        break;
    }
    default:
        break;
    }
} 

/*
* 启动网关配对定时器
*/
static int start_lora_pair_timer(neu_plugin_t *plugin)
{
    neu_event_timer_t *timer = NULL;

    neu_event_timer_param_t param = {
        .second      = 20,
        .millisecond = 0,
        .cb          = lora_pair_timer_callback,
        .usr_data    = plugin,
    };

    timer = neu_event_add_timer(plugin->tty_events, param);
    if (NULL == timer) {        
        return NEU_ERR_EINTERNAL;
    }

    plugin->pair_timer = timer;

    pair_on_change_chn(plugin,0,0,0);

    return 0;
}


/*
* 停止网关配置
*/
static int  stop_lora_pair_timer(neu_plugin_t *plugin)
{
    if (plugin->pair_timer) {
        neu_event_del_timer(plugin->tty_events, plugin->pair_timer);
        plugin->pair_timer = NULL;
        plog_notice(plugin, "lora pair timer stopped");
    }
    plugin->pair_mid = -1;  //停止配对，复位当前配对设备
}

/*
* 停止搜网
*/
int acme_lora_driver_stop_search(neu_plugin_t *plugin)
{
    int ret = 0;

    stop_lora_pair_timer(plugin);       //停止搜网停止器

    pair_on_change_chn(plugin,plugin->mesh_cfg->freq, plugin->mesh_cfg->chn, plugin->mesh_cfg->netid);      //网关切回预设网络
    neu_msleep(500);
    pair_on_change_chn(plugin,plugin->mesh_cfg->freq, plugin->mesh_cfg->chn, plugin->mesh_cfg->netid);      //网关切回预设网络
    return ret;
}

/**
 * 网关应答子设备入网请求
 */
int wave_mesh_ack_join_request(neu_plugin_t *plugin,char * eui,int deviceType)
{
    if(plugin == NULL || eui == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }
    nlog_debug("Ack subDevice join request start....");

    int ret = 0;
    uint8_t broadcast[32] = {0xeb, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x30, 0x00, 0x00};
    uint16_t t_crc16=0;
    CB_FLAG_0 cbflag;
    cbflag.flag0 = 0x18;
    uint8_t len = 1;

    plugin->wm_cfg->flEmpty.bits.freq_band = plugin->mesh_cfg->freq;                                     //频段 0-433Mhz 1-462Mhz 2-478Mhz 3-502Mhz
    plugin->wm_cfg->flag0.bits.pa_index = plugin->mesh_cfg->pa_index;                                           //功率 -20dbm
    plugin->wm_cfg->chnBaud.bits.signalChn = plugin->mesh_cfg->chn;                                      //信道 1
    plugin->wm_cfg->chnBaud.bits.baudIndex = CFG_CHN_BAUD;                             //波特率必须115200
    plugin->wm_cfg->flWake = 0;                                                        //距离等级0
    plugin->wm_cfg->netID[0] = (uint8_t)(plugin->mesh_cfg->netid >> 16);
    plugin->wm_cfg->netID[1] = (uint8_t)(plugin->mesh_cfg->netid >> 8);
    plugin->wm_cfg->netID[2] = (uint8_t)(plugin->mesh_cfg->netid);

    //装载数据
    broadcast[len++] = deviceType;
	for(int i=0;i<8;i++){
        broadcast[len++] = eui[i];
	}
	broadcast[len++] = 16;    //data length
	broadcast[len++] = 0x30;  //rsp
	for(int i=0;i<8;i++){
        broadcast[len++] = plugin->mesh_cfg->eui[i];
	}

    broadcast[len++]  = plugin->wm_cfg->flEmpty.flEmpty;//频段 --index 46
    broadcast[len++]  = plugin->wm_cfg->chnBaud.val;    //信道 --index 4
    cbflag.bits.pa_index = plugin->mesh_cfg->pa_index; 
    broadcast[len++] = cbflag.flag0;          //功率 --index 0
    broadcast[len++]  =  plugin->wm_cfg->netID[0];
    broadcast[len++]  = plugin->wm_cfg->netID[1];
    broadcast[len++]  = plugin->wm_cfg->netID[2];
    broadcast[len++]  = plugin->wm_cfg->flWake;                   //距离等级
    //broadcast[len++]  = wm_cfg.flag1.bits.mac_head_out;  //扩展头标志      从机不使用

	t_crc16 = CRC16(broadcast, len);
        
    broadcast[len++]  = (uint8_t)(t_crc16>>8);
    broadcast[len++]  = (uint8_t) t_crc16;
    
    printf("WAVEMESH allow you in --->");
    for(int i=0;i<len;i++){
        printf("%02x ",broadcast[i]);
    }
    printf("<--lora_ack\n");
	
    
    //写串口
    ret = neu_conn_send(plugin->lora_tty_conn, broadcast, len);
    return ret;
}

void init_wave_mesh_cfg(neu_plugin_t *plugin,cfgBlock* cfgBlk)
{
    uint8_t sTemp[15]={0xEB, 0x01, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x02, 0xCC, 0x01, 0x39, 0x04};
    uint16_t TailCRC16 = 0;
    //char gwHexEui[8] = {0};
    
    sTemp[1] = BEC_22L;

    StringToBuff("1300E233B06F230A",plugin->mesh_cfg->eui);  //网关 eui  测试号,后续通过网关设备节点传入该参数
    plugin->mesh_cfg->netid = 0;                          //网络ID
    plugin->mesh_cfg->chn   = 18;                        //信道
    plugin->mesh_cfg->freq  = 1;                       //频段
    plugin->mesh_cfg->pa_index  = 1;                   //功率
    plugin->mesh_cfg->baudIndex = CFG_CHN_BAUD;                  //波特率



    memcpy(&sTemp[2], plugin->mesh_cfg->eui, 8);
    TailCRC16 = CRC16(sTemp, 13);
    sTemp[13] = (TailCRC16>>8);
    sTemp[14] = TailCRC16;
   
   /*beMasterFlag       = atoi(ReadProfileString(PLF_CFG_FILE, "lora", "BE_MASTER"));
    iom_update_type    = atoi(ReadProfileString(PLF_CFG_FILE, "lora", "UPDATE_TYPE"));
    extend_info_enable = atoi(ReadProfileString(PLF_CFG_FILE, "lora", "EXTEND_INFO"));
    rf_rate_index      = atoi(ReadProfileString(PLF_CFG_FILE, "lora", "RF_RATE_INDEX"));
    submod_unfollow    = atoi(ReadProfileString(PLF_CFG_FILE, "lora", "SUBMOD_UNFOLLOW"));
    mq_cycle_report    = atoi(ReadProfileString(PLF_CFG_FILE, "lan1", "CYCLE_REPORT"));
    */

    uint8_t beMasterFlag = 1;//默认做主机，后续将变量整合到 neu_plugin_t 结构体中
    uint8_t iom_update_type = 0;
    uint8_t extend_info_enable = 0;
    uint8_t rf_rate_index = 0;
    uint8_t sig_chn_cflag = 0;
    uint8_t submod_unfollow = 0;
    int mq_cycle_report=0;
    
    if (beMasterFlag) { //作主机配置初始化
        cfgBlk->flag0.flag0 = CFG_FLAG0	;
        cfgBlk->flag1.flag1 = CFG_FLAG1	;
        cfgBlk->flag2.flag2 = CFG_FLAG2	;
        cfgBlk->flag3.flag3 = CFG_FLAG3	;
        cfgBlk->chnBaud.val = CFG_CHN_BAUD ;
        cfgBlk->uartLen = CFG_UART_LEN ;
        cfgBlk->uartStart = CFG_UART_START ;
        cfgBlk->uartEnd = CFG_UART_END ;
        cfgBlk->addDesOff = CFG_ADD_OFF_DES ;
        cfgBlk->addSrcOff = CFG_ADD_OFF_SRC ;
        cfgBlk->lenOff = CFG_LEN_OFF ;
        cfgBlk->lenAmend = CFG_LEN_AMEND;

        cfgBlk->macAdd[0] = CFG_MAC0;cfgBlk->macAdd[1] = CFG_MAC1;
        cfgBlk->macAdd[2] = CFG_MAC2;cfgBlk->macAdd[3] = CFG_MAC3;
        cfgBlk->macAdd[4] = CFG_MAC4;cfgBlk->macAdd[5] = CFG_MAC5;

        cfgBlk->netID[0] = CFG_NET_ID0;cfgBlk->netID[1] = CFG_NET_ID1;cfgBlk->netID[2] = CFG_NET_ID2;

        cfgBlk->affixSync = CFG_AFFIX_START ;
        cfgBlk->wildcardB = CFG_WILD_BC;
        cfgBlk->wildcardM = CFG_WILD_MC;
        cfgBlk->wakeDelay = CFG_POWERON_DELAY	;
        cfgBlk->respTimeout = CFG_RESP_TIMEOUT;
        cfgBlk->sleepAsync = CFG_SLEEP_ASYNC;
        cfgBlk->sleepSync = CFG_SLEEP_SYNC;
        cfgBlk->sleepAuto = CFG_SLEEP_AUTO;
        cfgBlk->infoOff = CFG_INFO_OFF;

        cfgBlk->flAck.flack = CFG_LEN_ACK;
        memset(cfgBlk->pktAck,0 ,15);
        //memcpy(cfgBlk->pktAck, sTemp, 15);


        cfgBlk->flEmpty.flEmpty = CFG_LEN_EMPTY;
        memset(cfgBlk->pktEmpty,0 ,15);

        cfgBlk->flWake = (rf_rate_index<<4|CFG_LEN_WAKE);
        memset(cfgBlk->pktWake,0 ,15);

        cfgBlk->flSent = CFG_LEN_SENT;
        memset(cfgBlk->pktSent,0 ,15); 

        cfgBlk->flProbe = CFG_LEN_PROBE;
        memset(cfgBlk->pktProbe,0 ,23);
    }else{ //作从机配置初始化
    
        cfgBlk->flag0.flag0 = sCFG_FLAG0	;
        cfgBlk->flag1.flag1 = sCFG_FLAG1	;
        cfgBlk->flag2.flag2 = sCFG_FLAG2	;
        cfgBlk->flag3.flag3 = sCFG_FLAG3	;
        cfgBlk->chnBaud.val = sCFG_CHN_BAUD ;
        cfgBlk->uartLen     = sCFG_UART_LEN ;
        cfgBlk->uartStart   = sCFG_UART_START ;
        cfgBlk->uartEnd     = sCFG_UART_END ;
        cfgBlk->addDesOff   = sCFG_ADD_OFF_DES ;
        cfgBlk->addSrcOff   = sCFG_ADD_OFF_SRC ;
        cfgBlk->lenOff      = sCFG_LEN_OFF ;
        cfgBlk->lenAmend    = sCFG_LEN_AMEND;

        cfgBlk->macAdd[0] = sCFG_MAC0;cfgBlk->macAdd[1] = sCFG_MAC1;
        cfgBlk->macAdd[2] = sCFG_MAC2;cfgBlk->macAdd[3] = sCFG_MAC3;
        cfgBlk->macAdd[4] = sCFG_MAC4;cfgBlk->macAdd[5] = sCFG_MAC5;

        cfgBlk->netID[0] = sCFG_NET_ID0;cfgBlk->netID[1] = sCFG_NET_ID1;cfgBlk->netID[2] = sCFG_NET_ID2;

        cfgBlk->affixSync = sCFG_AFFIX_START ;
        cfgBlk->wildcardB = sCFG_WILD_BC;
        cfgBlk->wildcardM = sCFG_WILD_MC;
        cfgBlk->wakeDelay = sCFG_POWERON_DELAY	;
        cfgBlk->respTimeout = sCFG_RESP_TIMEOUT;
        cfgBlk->sleepAsync = sCFG_SLEEP_ASYNC;
        cfgBlk->sleepSync = sCFG_SLEEP_SYNC;
        cfgBlk->sleepAuto = sCFG_SLEEP_AUTO;
        cfgBlk->infoOff = sCFG_INFO_OFF;

        cfgBlk->flAck.flack = sCFG_LEN_ACK;
        memset(cfgBlk->pktAck,0 ,15);
        //memcpy(cfgBlk->pktAck, sTemp, 15);

        cfgBlk->flEmpty.flEmpty = sCFG_LEN_EMPTY;
        memset(cfgBlk->pktEmpty,0 ,15);

        cfgBlk->flWake = ((rf_rate_index<<4|CFG_LEN_WAKE));
        memset(cfgBlk->pktWake,0 ,15);

        //cfgBlk->flSent = sCFG_LEN_SENT;
        //memset(cfgBlk->pktSent,0 ,15);
        cfgBlk->flSent = 15;
        memcpy(cfgBlk->pktSent, sTemp, 15);

        cfgBlk->flProbe = sCFG_LEN_PROBE;
        memset(cfgBlk->pktProbe,0 ,23);
    }


    cfgBlk->flEmpty.bits.freq_band = plugin->mesh_cfg->freq; //频段  1
    cfgBlk->flag0.bits.pa_index = plugin->mesh_cfg->pa_index;   //功率  0
    cfgBlk->chnBaud.bits.signalChn = plugin->mesh_cfg->chn;  //信道  10
    cfgBlk->chnBaud.bits.baudIndex = plugin->mesh_cfg->baudIndex;                           //波特率必须115200
    cfgBlk->flag1.bits.mac_head_out = extend_info_enable;
    cfgBlk->netID[0] = (uint8_t)(plugin->mesh_cfg->netid >> 16);
    cfgBlk->netID[1] = (uint8_t)(plugin->mesh_cfg->netid >> 8);
    cfgBlk->netID[2] = (uint8_t)(plugin->mesh_cfg->netid);
    cfgBlk->flWake = (rf_rate_index<<4|CFG_LEN_WAKE);   //速率等级

    printf("WAVEMESH->freq:%d, chn:%d, power:%d, netid:%d\n",cfgBlk->flEmpty.bits.freq_band,
                    cfgBlk->chnBaud.bits.signalChn,1,6);
}




/*
* Lora 串口初始化
*/
int acme_lora_serial_init(neu_plugin_t *plugin,char *device,neu_conn_tty_baud_e baud, neu_conn_tty_data_e data, neu_conn_tty_parity_e parity, neu_conn_tty_stop_e  stop,uint16_t timeout)
{
    int ret = 0;

    if(plugin == NULL || device == NULL) return -1;

    
    neu_conn_param_t param     = { 0 };
    param.log              = plugin->common.log;
    param.type = NEU_CONN_TTY_CLIENT;

    param.params.tty_client.device  = device;
    param.params.tty_client.baud    = baud;
    param.params.tty_client.data    = data;
    param.params.tty_client.parity  = parity;
    param.params.tty_client.stop    = stop;
    param.params.tty_client.timeout = timeout;

    plugin->lora_tty_conn = neu_conn_new(&param, (void *) plugin, lora_tty_conn_connected,
                         lora_tty_conn_disconnected);


    plugin->tty_events = neu_event_new();

    //Lora 接收缓冲区
    plugin->pMeshRcv = RingBuffer_Malloc(BUFF_CASH_LEN);
    RingBuffer_Reset(plugin->pMeshRcv);
  
    plugin->wm_cfg = (cfgBlock *)calloc(1, sizeof(cfgBlock));

    plugin->tty_pack_sta = (tty_pack_sta_t * )calloc(1,sizeof(tty_pack_sta_t));
    plugin->tty_pack_sta->_state = PCK_HED;

    plog_notice(plugin, "Lora tty:%s init success", device);
    return ret;
}

/*
* Lora 初始化
*/
int acme_lora_init(neu_plugin_t *plugin)
{
    int ret = 0;

    lora_reset_gpio_init();
    
    //Lora 串口初始化
    ret = acme_lora_serial_init(plugin, SPT_LORA_TTY_DEV, SPT_LORA_TTY_BAUD, SPT_LORA_TTY_DATA_BIT, SPT_LORA_TTY_PARITY, SPT_LORA_TTY_STOP_BIT, SPT_LORA_TTY_TIMEOUT);
    if(ret != 0){
        plog_notice(plugin, "Lora tty %s init faild !", SPT_LORA_TTY_DEV);
    }

    plugin->pair_mode = 0;     //当前配对模式

    plugin->mesh_cfg = (lora_mesh_cfg *)calloc(1,sizeof(lora_mesh_cfg));

    
    //reset_lora_Ra07_mode(); //Lora 模块拉复位

    
    plog_notice(plugin, " ************** Lora init ok ***************");

    return ret;
}




/*
* lora 发送msg 数据
*/
int acme_lora_msg_send(neu_plugin_t *plugin,uint8_t modeType,uint8_t rsp, char *eui, uint8_t *buff, uint16_t buff_len)
{
    int ret = 0;
    char uart_lora_write_buf[200] = {0x55,0xcc,0x05,0x01,0x27,0x00,0x00,0x00,0x00,0x00};

    //TODO: Lora 数据打包
    //...
    uint8_t i = 0;
	uint8_t check_sum = 0;
	uint8_t length = 0;

	uint16_t t_crc16=0;
	uart_lora_write_buf[0] = GWMP_HEADER_STX;//head
	uart_lora_write_buf[1] = modeType;//devicetype
	for(i=0;i<8;i++){//eui
		uart_lora_write_buf[2+i] =  eui[i];
	}
	uart_lora_write_buf[10] =  buff_len+1;//data len
	if(uart_lora_write_buf[10]){
		uart_lora_write_buf[11] =  rsp;//rsp
		for(i=0; i<buff_len; i++){
			uart_lora_write_buf[12+i] = buff[i];
		}
	}
	check_sum = uart_lora_write_buf[10]+11;//crc_length
    t_crc16 = CRC16((uint8_t *)uart_lora_write_buf, check_sum);
        
        
	uart_lora_write_buf[check_sum] = (uint8_t)(t_crc16>>8);
	uart_lora_write_buf[check_sum+1]   = (uint8_t) t_crc16;

    length = check_sum+2;


    printf("wavemesh_data_send--->");
    for(int i=0;i<length;i++){
        printf("%02x ",uart_lora_write_buf[i]);
    }
    printf("<--lora_snd\n");
	

	//write(lora_fd, uart_lora_write_buf, length);
    //写如串口
    ret = neu_conn_send(plugin->lora_tty_conn, uart_lora_write_buf, length);

    return ret;
}

void lora_recv_buffer_clear(tty_pack_sta_t * pack_sta)
{
    if(pack_sta == NULL) return;

    memset(pack_sta->lora_raw_data, 0, pack_sta->_pos);
    pack_sta->_state = PCK_HED;
    pack_sta->_pos = 0;
}

#if 1
static void at_ringbuffer_parser(neu_plugin_t *plugin, RingBuffer *pRingBuff)
{
    uint8_t i;
    uint8_t blen=0;
    uint8_t reg;
    if(plugin == NULL || pRingBuff == NULL) return;

    int beMasterFlag = 1;
    
    blen = RingBuffer_Len(pRingBuff);
    
    for (i=0; i<blen; i++) {
        RingBuffer_Out(pRingBuff, &reg, 1);
        
        printf("%02x ",reg);
        
        //EB 01 11 22 33 44 55 66 77 88 0B 31 11 22 33 44 55 66 77 88 99 10 2D 2B(crc 高位在前)
        if((plugin->tty_pack_sta->_state == PCK_HED) && (reg == GWMP_HEADER_STX)){
            plugin->tty_pack_sta->_state = PCK_DATA;
            plugin->tty_pack_sta->_pos  = 0;
            plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
        }
        else if((plugin->tty_pack_sta->_state == PCK_HED) && (reg == 'A')){
            plugin->tty_pack_sta->_state = PCK_CCFG;
            plugin->tty_pack_sta->_pos  = 0;
            plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
        }  
        //7e db 00 02 f5 a0 00 02 f6 94 df e7 01 31 00 [eb ...]
        else if((plugin->tty_pack_sta->_state == PCK_HED) && (reg == 0x7e)){
            plugin->tty_pack_sta->_state = PCK_EXTH;
            plugin->tty_pack_sta->_pos  = 0;
            plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
        } 
        else{
            if(plugin->tty_pack_sta->_state == PCK_DATA){
                plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
                if(plugin->tty_pack_sta->_pos > 240){
                    lora_recv_buffer_clear(plugin->tty_pack_sta);
                    return;
                }

                if (plugin->tty_pack_sta->_pos >= 13) {
                    if (plugin->tty_pack_sta->_pos == (plugin->tty_pack_sta->lora_raw_data[10]+13)) {
                        if (0 == CRC16(plugin->tty_pack_sta->lora_raw_data, plugin->tty_pack_sta->_pos)) {
                            //loraDev.loraRxMoniTick = 0;
                            lpwan_wavemesh_t wave_mesh_pck = {0};
                            //memset(&wave_mesh_pck, 0, sizeof(lpwan_wavemesh_t));
                            memcpy(&wave_mesh_pck, plugin->tty_pack_sta->lora_raw_data, plugin->tty_pack_sta->_pos);
                            #if 1
                           // if(gwlog_upload){
                            	char hexs[600]={0};
                            	MESH_Hex2Str((const char*)plugin->tty_pack_sta->lora_raw_data, hexs, plugin->tty_pack_sta->_pos);
                            	nlog_debug("<len:%d> mesh--> %s\n", plugin->tty_pack_sta->_pos, hexs);
                            //}else{                            	
                            	printf(" <MESH> ->devType:%d, RSP:%x\n",wave_mesh_pck.DeviceType, wave_mesh_pck.RSP);                            	  
                            //}
                            #endif
                            acme_lora_process_protocol_buf(plugin,&wave_mesh_pck);
                        }
                        lora_recv_buffer_clear(plugin->tty_pack_sta);
                    }
                }
            }
            /*lora_rcv-->41 54 57 52 41 
                     00 1b 13 88 00 07 07 eb 0d 02 02 0a 0d 30 32 41 30 30 44 00 
                     00 00 7e ff ff 00 02 00 00 00 ff 50 00 00 00 00 00 00 00 00 
                     00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 
                     00 00 00 00 0d */
            /*lora_rcv-->41 54 52 44 82 
                     00 1b 13 88 00 07 07 eb 0d 02 02 0a 0d 30 32 41 30 30 44 00 
                     00 00 7e ff ff 00 02 00 00 00 ff 50 00 00 00 00 00 00 00 00 
                     00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 
                     00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 
                     00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 
                     00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 dc 
                     e4 5a 6a 61 8a fb f5 a1 0d 0d*/
            else if(plugin->tty_pack_sta->_state == PCK_CCFG){
                if(plugin->tty_pack_sta->_pos<4){
                    if((reg == 'T') || (reg == 'W') || (reg == 'R') || (reg == 'D')){
                        plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
                    }else{
                         lora_recv_buffer_clear(plugin->tty_pack_sta);
                    }
                }else{
                    plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
                    if(plugin->tty_pack_sta->_pos > (plugin->tty_pack_sta->lora_raw_data[4]+5)){
                         lora_recv_buffer_clear(plugin->tty_pack_sta);
                    }
                    if(plugin->tty_pack_sta->_pos == (plugin->tty_pack_sta->lora_raw_data[4]+5)){
                        //.loraRxMoniTick = 0;
                        //(plugin->tty_pack_sta->lora_raw_data[6], plugin->tty_pack_sta->lora_raw_data[4]-1);
                        
                        lora_recv_buffer_clear(plugin->tty_pack_sta);
                    }
                }
                
            }
            //7e db 00 02 f5 a0 00 02 f6 94 df e7 01 31 00
            /*else if (plugin->tty_pack_sta->_state == PCK_EXTH) {
                plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
                if(beMasterFlag != WAVE_MESH_MASTER){ 
                    if(plugin->tty_pack_sta->_pos==4){// 从机 获取 下行扩展信息
                        if(extend_info_enable){
                            gateway_cfg.LoraCfg.dnrssi = (plugin->tty_pack_sta->lora_raw_data[1]/2-150);
                            gateway_cfg.LoraCfg.distoroot = plugin->tty_pack_sta->lora_raw_data[2];
                            
                            printf("dnRSSI:[%d], Distance to root:[%d]\n",\
                                                              gateway_cfg.LoraCfg.dnrssi,gateway_cfg.LoraCfg.distoroot);
                        }
                        lora_recv_buffer_clear(plugin->tty_pack_sta);
                    }
                }else{
                    if(plugin->tty_pack_sta->_pos==15){// 主机 获取 上行扩展信息
                        if(extend_info_enable){
                            ArrayToStr(plugin->tty_pack_sta->lora_raw_data[2], 4, gateway_cfg.LoraCfg.srcid);
                            ArrayToStr(plugin->tty_pack_sta->lora_raw_data[6], 4, gateway_cfg.LoraCfg.firid);
                            gateway_cfg.LoraCfg.uprssi = (plugin->tty_pack_sta->lora_raw_data[10]/2-150);
                            gateway_cfg.LoraCfg.dnrssi = (plugin->tty_pack_sta->lora_raw_data[11]/2-150);
                            gateway_cfg.LoraCfg.distoroot = plugin->tty_pack_sta->lora_raw_data[12];
                            if(gptDebugFlag)
                                printf("srcID:[%s], firId:[%s], unRSSI:[%d], dnRSSI:[%d], Distance to root:[%d]\n",\
                                    gateway_cfg.LoraCfg.srcid, gateway_cfg.LoraCfg.firid,\
                                    gateway_cfg.LoraCfg.uprssi,gateway_cfg.LoraCfg.dnrssi,gateway_cfg.LoraCfg.distoroot);
                        }
                        lora_recv_buffer_clear(plugin->tty_pack_sta);
                    }
                }
            }*/
        }

  
    }

}
#endif

/*
* lora 接收数据
*/
int acme_lora_msg_recv(enum neu_event_io_type type, int fd, void *usr_data)
{
    neu_plugin_t *plugin = (neu_plugin_t *)usr_data;
    if(plugin == NULL){
        nlog_warn("tty recv parameter error.");
        return -1;
    }

    int ret = 0;
    char recv_buff[512] = {0};
    int nByte = 0;

    if (type != NEU_EVENT_IO_READ) {
        nlog_warn("read close!");
        return -1;
    }

    nByte = read(fd, recv_buff, sizeof(recv_buff));
    if (nByte > 0) {
        
        //收到数据 加入环形队列
        RingBuffer_In(plugin->pMeshRcv, recv_buff, nByte);   

        printf("Lora tty recv data(%d): *********** -->",nByte);
        for(int i=0;i<nByte;i++){
        	printf("%02x ", recv_buff[i]);
        }
        printf(" ----< *******************\r\n");
    }

    at_ringbuffer_parser(plugin,plugin->pMeshRcv);     //串口数据解析

    return ret;
}   



/*
* lora 串口启动接收
*/
int lora_tty_start(neu_plugin_t *plugin)
{
    int ret = 0;

    neu_conn_start(plugin->lora_tty_conn);
    neu_conn_connect(plugin->lora_tty_conn);

    int fd = neu_conn_fd(plugin->lora_tty_conn);
    if (fd <= 0) {
        return -1;
    }

    neu_event_io_param_t ioparam = { 0 };
    ioparam.fd                   = fd;
    ioparam.cb                   = acme_lora_msg_recv;
    ioparam.usr_data             = plugin;

    neu_event_io_t * event  = neu_event_add_io(plugin->tty_events, ioparam);
    if(event == NULL){
        plog_notice(plugin, "acme_lora_msg_recv add io error.");
        return -1;
    }
    plugin->tty_recv_io = event;


    plog_notice(plugin, "Lora tty recv start...");


    //此处还需要进行 模块寄存器相关初始化 
    //TODO .....
    init_wave_mesh_cfg(plugin,plugin->wm_cfg);
    atwr_wave_mesh_module_cfg(plugin);



    return ret;
}

/*
* Lora 串口停止接收
*/
int lora_tty_stop(neu_plugin_t *plugin)
{
    int ret = 0;

    neu_conn_stop(plugin->lora_tty_conn);       //断开串口连接

    neu_event_del_io(plugin->tty_events, plugin->tty_recv_io);

    RingBuffer_Free(plugin->pMeshRcv);

    stop_lora_pair_timer(plugin);            //停止配对

    neu_devices_manager_destroy(plugin->subDevice_manager);

    return ret;
}

int lora_parameter_setting(neu_plugin_t *plugin)
{
    if(plugin->pair_timer != NULL){
        stop_lora_pair_timer(plugin);            //停止配对
    }
    usleep(100);

    start_lora_pair_timer(plugin);        //启动配网定时器

    return 0;
}
